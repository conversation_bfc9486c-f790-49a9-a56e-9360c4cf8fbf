#include <iostream>
#include <unordered_map>
#include <map>
#include <fstream>
#include <vector>
#include "enum.h"
#include "globalFile.h"
#include "Identity.h"
#include "student.h"
#include "teacher.h"
#include "manager.h"

using namespace std;

// 用户信息结构体
struct UserInfo
{
	int id;
	string name;
	string password;

	UserInfo(int id = 0, const string &name = "", const string &password = "")
		: id(id), name(name), password(password)
	{
	}
};

// 密码验证函数 - 一次性读取文件到内存
void judgmentPwd(int id, const string &name, const string &pwd, const string &fileName, bool isManager = false)
{
	ifstream ifs(fileName, ios::in);
	if (!ifs.is_open())
	{
		cout << "file open failed\n";
		return;
	}

	// 一次性读取所有用户信息到内存
	vector<UserInfo> users;

	if (isManager)
	{
		// 管理员文件格式：name password
		string fName, fPwd;
		while (ifs >> fName >> fPwd)
		{
			users.emplace_back(0, fName, fPwd); // 管理员ID设为0
		}
	}
	else
	{
		// 学生/教师文件格式：id name password
		int fId;
		string fName, fPwd;
		while (ifs >> fId >> fName >> fPwd)
		{
			users.emplace_back(fId, fName, fPwd);
		}
	}
	ifs.close();

	// 在内存中查找匹配的用户
	/* bool loginSuccess = false;
	for (const auto& user : users)
	{
		if (isManager)
		{
			// 管理员只需要验证用户名和密码
			if (user.name == name && user.password == pwd)
			{
				loginSuccess = true;
				break;
			}
		}
		else
		{
			// 学生/教师需要验证ID、用户名和密码
			if (user.id == id && user.name == name && user.password == pwd)
			{
				loginSuccess = true;
				break;
			}
		}
	} */

	// 使用 std::find_if 替代手动循环
	auto it = std::find_if(users.begin(), users.end(), [&](const UserInfo &user)
						   {
			if (isManager) {
				return user.name == name && user.password == pwd;
			}
			else {
				return user.id == id && user.name == name && user.password == pwd;
			} });

	bool loginSuccess = (it != users.end());

	// 输出结果
	if (loginSuccess)
	{
		cout.clear();
		cout << "login success!" << endl;
	}
	else
	{
		cout << "login failed!" << endl;
	}
}

void login(unordered_map<int, UserTypeInfo>::iterator it)
{
	Identity *person = NULL; // 使用基类指针
	int id;
	string name;
	string pwd;

	UserType userType = static_cast<UserType>(it->first);
	if (userType == USER_STUDENT)
	{
		cout << "please input student ID:";
		cin >> id;
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		judgmentPwd(id, name, pwd, it->second.fileName);
		person = new Student(id, name, pwd);
	}
	else if (userType == USER_TEACHER)
	{
		cout << "please input teacher ID:";
		cin >> id;
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		person = new Teacher(id, name, pwd);
		judgmentPwd(id, name, pwd, it->second.fileName);
	}
	else if (userType == USER_MANAGER)
	{
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		person = new Manager(name, pwd);
		judgmentPwd(0, name, pwd, it->second.fileName, true);
	}

	// person->operMenu(); // 调用操作菜单函数
}

// 方案1：使用 unordered_map 存储完整的用户信息 - O(1) 查找效率
unordered_map<int, UserTypeInfo> userTypeMap = {
	{USER_STUDENT, {"Student", STUDENT_FILE}},
	{USER_TEACHER, {"Teacher", TEACHER_FILE}},
	{USER_MANAGER, {"Manager", ADMIN_FILE}}};

int main()
{
	int select = 0;
	const int WIDTH = 28;
	while (true)
	{
		system("cls");
		cout << "============================" << endl;
		// 遍历显示所有用户类型选项
		// 旧写法
		/*for (const auto& pair : userTypeMap) {
			cout << pair.first << "." << pair.second.label << endl;
		}*/
		// 新写法 C++20 结构化绑定
		for (const auto &[key, value] : userTypeMap)
		{
			const string s = to_string(key) + "." + value.label;
			cout.width(WIDTH); // 只作用到下一个cout，这样写b不行：cout << a << b
			cout << s << endl;
		}
		cout.width(WIDTH);
		cout << "0.exit" << endl;
		cout << "============================" << endl;
		cout << "please input：";
		cin >> select;

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end())
		{
			cout << "you select " << it->second.label << endl;
			cout << "file path: " << it->second.fileName << endl;
			login(it);
			system("pause");
		}
		else if (select == 0)
		{
			exit(0);
		}
		else
		{
			cout << "input error, please input again!" << endl;
			system("pause");
		}
	}
	return 0;
}